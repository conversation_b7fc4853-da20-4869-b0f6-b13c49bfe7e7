<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice & Gesture Control Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-around;
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .status-item {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-width: 150px;
        }
        
        .status-item.active {
            background: #d4edda;
            border: 2px solid #28a745;
        }
        
        .status-item.inactive {
            background: #f8d7da;
            border: 2px solid #dc3545;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .control-panel, .command-panel {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .control-panel h2, .command-panel h2 {
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }
        
        .control-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-transform: uppercase;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }
        
        .command-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            background: white;
        }
        
        .command-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
            margin-bottom: 10px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        
        .command-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .test-results {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background: white;
            border: 1px solid #dee2e6;
        }
        
        .alert {
            padding: 12px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 10px;
            }
            
            .control-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 Voice & Gesture Control</h1>
            <p>Hands-free laptop control system</p>
        </div>
        
        <div class="status-bar">
            <div class="status-item" id="system-status">
                <h3>System</h3>
                <p id="system-text">Loading...</p>
            </div>
            <div class="status-item" id="voice-status">
                <h3>Voice Control</h3>
                <p id="voice-text">Loading...</p>
            </div>
            <div class="status-item" id="gesture-status">
                <h3>Gesture Control</h3>
                <p id="gesture-text">Loading...</p>
            </div>
        </div>
        
        <div class="main-content">
            <div class="control-panel">
                <h2>🎛️ System Controls</h2>
                
                <div class="control-buttons">
                    <button class="btn btn-success" onclick="controlSystem('start')">Start System</button>
                    <button class="btn btn-danger" onclick="controlSystem('stop')">Stop System</button>
                    <button class="btn btn-primary" onclick="controlSystem('toggle_voice')">Toggle Voice</button>
                    <button class="btn btn-primary" onclick="controlSystem('toggle_gesture')">Toggle Gesture</button>
                </div>
                
                <h3>🧪 Component Tests</h3>
                <div class="control-buttons">
                    <button class="btn btn-warning" onclick="testComponent('microphone')">Test Microphone</button>
                    <button class="btn btn-warning" onclick="testComponent('camera')">Test Camera</button>
                </div>
                
                <div class="test-results" id="test-results" style="display: none;">
                    <h4>Test Results:</h4>
                    <div id="test-output"></div>
                </div>
            </div>
            
            <div class="command-panel">
                <h2>📋 Available Commands</h2>
                
                <h3>Voice Commands</h3>
                <div class="command-list" id="voice-commands">
                    Loading commands...
                </div>
                
                <h3>Gesture Commands</h3>
                <div class="command-list" id="gesture-commands">
                    Loading commands...
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // Socket event handlers
        socket.on('connect', function() {
            console.log('Connected to server');
            loadCommands();
        });
        
        socket.on('status_update', function(data) {
            updateStatus(data);
        });
        
        socket.on('status', function(data) {
            updateStatus(data);
        });
        
        // Update status display
        function updateStatus(data) {
            if (data.system) {
                const systemStatus = document.getElementById('system-status');
                const systemText = document.getElementById('system-text');
                
                if (data.system.running) {
                    systemStatus.className = 'status-item active';
                    systemText.textContent = 'Running';
                } else {
                    systemStatus.className = 'status-item inactive';
                    systemText.textContent = 'Stopped';
                }
                
                // Voice status
                const voiceStatus = document.getElementById('voice-status');
                const voiceText = document.getElementById('voice-text');
                
                if (data.system.voice_enabled) {
                    voiceStatus.className = 'status-item active';
                    voiceText.textContent = 'Enabled';
                } else {
                    voiceStatus.className = 'status-item inactive';
                    voiceText.textContent = 'Disabled';
                }
                
                // Gesture status
                const gestureStatus = document.getElementById('gesture-status');
                const gestureText = document.getElementById('gesture-text');
                
                if (data.system.gesture_enabled) {
                    gestureStatus.className = 'status-item active';
                    gestureText.textContent = 'Enabled';
                } else {
                    gestureStatus.className = 'status-item inactive';
                    gestureText.textContent = 'Disabled';
                }
            }
        }
        
        // Control system functions
        function controlSystem(action) {
            fetch(`/api/control/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                } else {
                    showAlert(data.error || 'Operation failed', 'danger');
                }
            })
            .catch(error => {
                showAlert('Network error: ' + error.message, 'danger');
            });
        }
        
        // Test components
        function testComponent(component) {
            const testResults = document.getElementById('test-results');
            const testOutput = document.getElementById('test-output');
            
            testResults.style.display = 'block';
            testOutput.innerHTML = '<div class="alert alert-info">Testing ' + component + '...</div>';
            
            fetch(`/api/test/${component}`)
            .then(response => response.json())
            .then(data => {
                const alertClass = data.success ? 'alert-success' : 'alert-danger';
                testOutput.innerHTML = `<div class="alert ${alertClass}">${data.message}</div>`;
            })
            .catch(error => {
                testOutput.innerHTML = '<div class="alert alert-danger">Test failed: ' + error.message + '</div>';
            });
        }
        
        // Load available commands
        function loadCommands() {
            fetch('/api/commands')
            .then(response => response.json())
            .then(data => {
                displayCommands('voice-commands', data.voice || []);
                displayCommands('gesture-commands', data.gesture || []);
            })
            .catch(error => {
                console.error('Error loading commands:', error);
            });
        }
        
        // Display commands in the UI
        function displayCommands(containerId, commands) {
            const container = document.getElementById(containerId);
            
            if (commands.length === 0) {
                container.innerHTML = '<p>No commands available</p>';
                return;
            }
            
            container.innerHTML = commands.map(command => 
                `<div class="command-item">
                    <span><strong>${command}</strong></span>
                </div>`
            ).join('');
        }
        
        // Show alert messages
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // Load initial data
        document.addEventListener('DOMContentLoaded', function() {
            loadCommands();
        });
    </script>
</body>
</html>
