# Voice and Gesture Control System

A Python-based voice and gesture control system that enables hands-free laptop control through voice commands and hand gestures.

## Features

### 🎤 Voice Control
- Real-time speech recognition with customizable wake word
- Configurable voice commands for system actions
- Noise cancellation and ambient noise adjustment
- Multi-language support

### 👋 Gesture Recognition
- MediaPipe-based hand gesture detection
- Support for basic gestures: thumbs up/down, open palm, fist, peace sign
- Real-time webcam processing
- Configurable detection sensitivity

### 🎛️ System Integration
- System tray application with start/stop controls
- Web-based dashboard for configuration and monitoring
- Background service operation
- Cross-platform compatibility (Windows, Linux, macOS)

### 🌐 Web Interface
- Real-time status monitoring
- Command configuration
- Component testing
- Settings management

## Installation

1. **Clone or download the project**
   ```bash
   cd voice_and_ges
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install additional system dependencies**
   
   **Windows:**
   - Install PyAudio: `pip install pyaudio`
   - For volume control: Download NirCmd and add to PATH
   
   **Linux:**
   ```bash
   sudo apt-get install python3-pyaudio portaudio19-dev
   sudo apt-get install alsa-utils  # for volume control
   ```
   
   **macOS:**
   ```bash
   brew install portaudio
   pip install pyaudio
   ```

## Quick Start

1. **Run the application**
   ```bash
   python main.py
   ```

2. **Access the web interface**
   - Open your browser and go to `http://127.0.0.1:5000`
   - Use the dashboard to control the system

3. **System tray controls**
   - Look for the system tray icon
   - Right-click for menu options
   - Start/stop the system from the tray

## Default Commands

### Voice Commands (say "computer" first)
- "open browser" → Opens default web browser
- "scroll down" → Scrolls down in current window
- "scroll up" → Scrolls up in current window
- "volume up" → Increases system volume
- "volume down" → Decreases system volume
- "close window" → Closes current window
- "minimize window" → Minimizes current window

### Gesture Commands
- **Thumbs Up** → Volume up
- **Thumbs Down** → Volume down
- **Open Palm** → Pause media
- **Fist** → Play media
- **Peace Sign** → Take screenshot

## Configuration

Edit `config.yaml` to customize:

- **Wake word**: Change the voice activation phrase
- **Camera settings**: Select camera index and detection confidence
- **Command mappings**: Add or modify voice/gesture commands
- **System settings**: Auto-start, tray behavior, etc.

## Testing Components

Use the web interface or system tray menu to test:
- **Microphone**: Verify speech recognition is working
- **Camera**: Check gesture detection functionality

## Troubleshooting

### Common Issues

1. **Microphone not working**
   - Check microphone permissions
   - Verify PyAudio installation
   - Test with different microphone devices

2. **Camera not detected**
   - Check camera permissions
   - Try different camera indices (0, 1, 2...)
   - Ensure no other applications are using the camera

3. **Voice recognition errors**
   - Check internet connection (Google Speech API)
   - Adjust energy threshold in config
   - Speak clearly after the wake word

4. **Gesture detection issues**
   - Ensure good lighting conditions
   - Keep hands visible in camera frame
   - Adjust detection confidence settings

### System Requirements

- **Python**: 3.7 or higher
- **RAM**: Minimum 4GB (8GB recommended)
- **Camera**: Any USB or built-in webcam
- **Microphone**: Any USB or built-in microphone
- **Internet**: Required for speech recognition

## Development

### Project Structure
```
voice_and_ges/
├── main.py                 # Main application entry point
├── config.yaml            # Configuration file
├── requirements.txt       # Python dependencies
├── modules/               # Core modules
│   ├── voice_control.py   # Voice recognition
│   ├── gesture_control.py # Gesture detection
│   ├── command_executor.py # Command execution
│   ├── system_tray.py     # System tray app
│   └── web_interface.py   # Web dashboard
├── templates/             # HTML templates
│   ├── dashboard.html     # Main dashboard
│   └── settings.html      # Settings page
└── logs/                  # Log files
```

### Adding New Commands

1. **Voice Commands**: Edit the `commands.voice` section in `config.yaml`
2. **Gesture Commands**: Edit the `commands.gesture` section in `config.yaml`
3. **Custom Actions**: Add new action methods in `command_executor.py`

### Extending Functionality

- **New Gestures**: Modify `_classify_gesture()` in `gesture_control.py`
- **Advanced Speech**: Integrate with other speech recognition services
- **Custom Actions**: Add system-specific commands in `command_executor.py`

## License

This project is open source. Feel free to modify and distribute.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs in the `logs/` directory
3. Test individual components using the web interface

## Future Enhancements

- Machine learning-based custom gesture training
- Voice command learning and adaptation
- Mobile app integration
- Cloud-based speech recognition options
- Advanced macro creation tools
