# Voice and Gesture Control System Configuration

# Voice Control Settings
voice:
  wake_word: "computer"
  language: "en-US"
  timeout: 5
  phrase_timeout: 0.3
  energy_threshold: 300
  dynamic_energy_threshold: true

# Gesture Recognition Settings
gesture:
  camera_index: 0
  detection_confidence: 0.7
  tracking_confidence: 0.5
  max_hands: 2
  gesture_hold_time: 1.0

# Command Mappings
commands:
  voice:
    "open browser": "open_browser"
    "scroll down": "scroll_down"
    "scroll up": "scroll_up"
    "volume up": "volume_up"
    "volume down": "volume_down"
    "close window": "close_window"
    "minimize window": "minimize_window"
  
  gesture:
    "thumbs_up": "volume_up"
    "thumbs_down": "volume_down"
    "open_palm": "pause_media"
    "fist": "play_media"
    "peace": "screenshot"

# System Settings
system:
  auto_start: false
  minimize_to_tray: true
  log_commands: true
  energy_saving: true

# Web Interface Settings
web:
  host: "127.0.0.1"
  port: 5000
  debug: false

# Logging
logging:
  level: "INFO"
  file: "logs/voice_gesture_control.log"
  max_size: "10MB"
  backup_count: 5
