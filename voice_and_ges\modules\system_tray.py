"""
System Tray Application
Provides system tray interface for controlling the voice and gesture system
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication, QSystemTrayIcon, QMenu, QAction, QMessageBox
from PyQt5.QtCore import QTimer, pyqtSignal, QObject
from PyQt5.QtGui import QIcon, QPixmap
import os

class SystemTrayApp(QObject):
    """System tray application for voice and gesture control"""
    
    # Signals
    start_requested = pyqtSignal()
    stop_requested = pyqtSignal()
    
    def __init__(self, main_app):
        super().__init__()
        self.main_app = main_app
        self.logger = logging.getLogger(__name__)
        
        # Initialize Qt Application
        self.qt_app = QApplication.instance()
        if self.qt_app is None:
            self.qt_app = QApplication(sys.argv)
        
        # Create system tray icon
        self.tray_icon = None
        self.create_tray_icon()
        
        # Status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # Update every 5 seconds
        
        self.logger.info("System Tray Application initialized")
    
    def create_tray_icon(self):
        """Create and setup system tray icon"""
        try:
            # Check if system tray is available
            if not QSystemTrayIcon.isSystemTrayAvailable():
                QMessageBox.critical(None, "System Tray",
                                   "System tray is not available on this system.")
                return False
            
            # Create tray icon
            self.tray_icon = QSystemTrayIcon()
            
            # Set icon (create a simple colored icon if no file exists)
            icon = self.create_icon()
            self.tray_icon.setIcon(icon)
            
            # Set tooltip
            self.tray_icon.setToolTip("Voice & Gesture Control")
            
            # Create context menu
            self.create_context_menu()
            
            # Connect signals
            self.tray_icon.activated.connect(self.on_tray_icon_activated)
            
            # Show tray icon
            self.tray_icon.show()
            
            self.logger.info("System tray icon created successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create system tray icon: {e}")
            return False
    
    def create_icon(self):
        """Create a simple icon for the system tray"""
        try:
            # Try to load icon file first
            icon_path = "assets/icon.png"
            if os.path.exists(icon_path):
                return QIcon(icon_path)
            
            # Create a simple colored pixmap
            pixmap = QPixmap(16, 16)
            pixmap.fill(self.qt_app.palette().color(self.qt_app.palette().Highlight))
            return QIcon(pixmap)
            
        except Exception as e:
            self.logger.error(f"Error creating icon: {e}")
            # Return default icon
            return self.qt_app.style().standardIcon(self.qt_app.style().SP_ComputerIcon)
    
    def create_context_menu(self):
        """Create context menu for system tray"""
        try:
            menu = QMenu()
            
            # Status section
            self.status_action = QAction("Status: Initializing...", self)
            self.status_action.setEnabled(False)
            menu.addAction(self.status_action)
            
            menu.addSeparator()
            
            # Control actions
            self.start_action = QAction("Start System", self)
            self.start_action.triggered.connect(self.start_system)
            menu.addAction(self.start_action)
            
            self.stop_action = QAction("Stop System", self)
            self.stop_action.triggered.connect(self.stop_system)
            self.stop_action.setEnabled(False)
            menu.addAction(self.stop_action)
            
            menu.addSeparator()
            
            # Voice control toggle
            self.voice_action = QAction("Voice Control: ON", self)
            self.voice_action.triggered.connect(self.toggle_voice_control)
            menu.addAction(self.voice_action)
            
            # Gesture control toggle
            self.gesture_action = QAction("Gesture Control: ON", self)
            self.gesture_action.triggered.connect(self.toggle_gesture_control)
            menu.addAction(self.gesture_action)
            
            menu.addSeparator()
            
            # Settings and info
            settings_action = QAction("Open Web Interface", self)
            settings_action.triggered.connect(self.open_web_interface)
            menu.addAction(settings_action)
            
            test_action = QAction("Test Components", self)
            test_action.triggered.connect(self.test_components)
            menu.addAction(test_action)
            
            menu.addSeparator()
            
            # Exit
            exit_action = QAction("Exit", self)
            exit_action.triggered.connect(self.exit_application)
            menu.addAction(exit_action)
            
            self.tray_icon.setContextMenu(menu)
            
        except Exception as e:
            self.logger.error(f"Error creating context menu: {e}")
    
    def on_tray_icon_activated(self, reason):
        """Handle tray icon activation"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.open_web_interface()
    
    def start_system(self):
        """Start the voice and gesture control system"""
        try:
            self.main_app.start()
            self.start_action.setEnabled(False)
            self.stop_action.setEnabled(True)
            self.show_message("System Started", "Voice and Gesture Control is now active")
            self.logger.info("System started via tray menu")
        except Exception as e:
            self.logger.error(f"Error starting system: {e}")
            self.show_message("Error", f"Failed to start system: {e}")
    
    def stop_system(self):
        """Stop the voice and gesture control system"""
        try:
            self.main_app.stop()
            self.start_action.setEnabled(True)
            self.stop_action.setEnabled(False)
            self.show_message("System Stopped", "Voice and Gesture Control has been stopped")
            self.logger.info("System stopped via tray menu")
        except Exception as e:
            self.logger.error(f"Error stopping system: {e}")
            self.show_message("Error", f"Failed to stop system: {e}")
    
    def toggle_voice_control(self):
        """Toggle voice control on/off"""
        try:
            self.main_app.toggle_voice_control()
            status = "ON" if self.main_app.voice_enabled else "OFF"
            self.voice_action.setText(f"Voice Control: {status}")
            self.show_message("Voice Control", f"Voice control turned {status}")
        except Exception as e:
            self.logger.error(f"Error toggling voice control: {e}")
    
    def toggle_gesture_control(self):
        """Toggle gesture control on/off"""
        try:
            self.main_app.toggle_gesture_control()
            status = "ON" if self.main_app.gesture_enabled else "OFF"
            self.gesture_action.setText(f"Gesture Control: {status}")
            self.show_message("Gesture Control", f"Gesture control turned {status}")
        except Exception as e:
            self.logger.error(f"Error toggling gesture control: {e}")
    
    def open_web_interface(self):
        """Open web interface in browser"""
        try:
            import webbrowser
            web_config = self.main_app.config.get('web', {})
            host = web_config.get('host', '127.0.0.1')
            port = web_config.get('port', 5000)
            url = f"http://{host}:{port}"
            webbrowser.open(url)
            self.logger.info(f"Opened web interface: {url}")
        except Exception as e:
            self.logger.error(f"Error opening web interface: {e}")
            self.show_message("Error", f"Failed to open web interface: {e}")
    
    def test_components(self):
        """Test system components"""
        try:
            # Test microphone
            mic_status, mic_msg = self.main_app.voice_controller.test_microphone()
            
            # Test camera
            cam_status, cam_msg = self.main_app.gesture_controller.test_camera()
            
            # Show results
            message = f"Microphone: {'✓' if mic_status else '✗'} {mic_msg}\n"
            message += f"Camera: {'✓' if cam_status else '✗'} {cam_msg}"
            
            self.show_message("Component Test Results", message)
            
        except Exception as e:
            self.logger.error(f"Error testing components: {e}")
            self.show_message("Error", f"Component test failed: {e}")
    
    def update_status(self):
        """Update status display"""
        try:
            if hasattr(self.main_app, 'running'):
                status = "Running" if self.main_app.running else "Stopped"
                self.status_action.setText(f"Status: {status}")
                
                # Update icon tooltip with more details
                voice_status = "ON" if getattr(self.main_app, 'voice_enabled', False) else "OFF"
                gesture_status = "ON" if getattr(self.main_app, 'gesture_enabled', False) else "OFF"
                
                tooltip = f"Voice & Gesture Control - {status}\n"
                tooltip += f"Voice: {voice_status} | Gesture: {gesture_status}"
                self.tray_icon.setToolTip(tooltip)
                
        except Exception as e:
            self.logger.error(f"Error updating status: {e}")
    
    def show_message(self, title, message):
        """Show system tray message"""
        try:
            self.tray_icon.showMessage(title, message, QSystemTrayIcon.Information, 3000)
        except Exception as e:
            self.logger.error(f"Error showing message: {e}")
    
    def exit_application(self):
        """Exit the application"""
        try:
            # Stop the main system
            if hasattr(self.main_app, 'stop'):
                self.main_app.stop()
            
            # Hide tray icon
            if self.tray_icon:
                self.tray_icon.hide()
            
            # Quit Qt application
            self.qt_app.quit()
            
            self.logger.info("Application exited")
            
        except Exception as e:
            self.logger.error(f"Error exiting application: {e}")
    
    def run(self):
        """Run the system tray application"""
        try:
            self.logger.info("Starting system tray application")
            return self.qt_app.exec_()
        except Exception as e:
            self.logger.error(f"Error running system tray: {e}")
            return 1
