import speech_recognition as sr
from gtts import gTTS
from playsound import playsound
from datetime import datetime
import requests
import os
import tempfile

# === Translate Text Using LibreTranslate API ===
def translate_text(text, source_lang, target_lang):
    url = "https://libretranslate.com/translate"
    payload = {
        'q': text,
        'source': source_lang,
        'target': target_lang,
        'format': 'text',
        'alternatives': 3,
        'api_key': ""
    }
    try:
        response = requests.post(url, data=payload)
        data = response.json()
        if 'translatedText' in data:
            return data['translatedText']
        else:
            print("API response error:", data)
            return "Translation Error"
    except Exception as e:
        print("Translation failed:", e)
        return "Translation Error"

# === Detect Basic Language ===
def detect_language(text):
    if any(c in text for c in "ಅಆಇಈಉಊಎ"):  # Kannada script
        return "kn"
    elif any(c in text for c in "अआइईउऊए"):  # Hindi script
        return "hi"
    else:
        return "en"

# === Speak Text Using gTTS + playsound ===
def speak(text, lang='en'):
    try:
        tts = gTTS(text=text, lang=lang)
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".mp3")
        temp_path = temp_file.name
        temp_file.close()
        tts.save(temp_path)
        playsound(temp_path)
        os.remove(temp_path)
    except Exception as e:
        print("Error in speak():", e)

# === Handle English Commands ===
def get_response(command):
    command = command.lower()
    now = datetime.now()

    if "your name" in command:
        return "My name is MultiLingual Assistant."
    elif "time" in command:
        return "The current time is " + now.strftime("%H:%M")
    elif "date" in command:
        return "Today's date is " + now.strftime("%Y-%m-%d")
    elif "how are you" in command:
        return "I am fine, thank you!"
    else:
        return "Sorry, I didn't understand that."

# === Main Function ===
def run_assistant():
    recognizer = sr.Recognizer()

    with sr.Microphone() as source:
        print("Listening... (Speak in English, Kannada, or Hindi)")
        recognizer.adjust_for_ambient_noise(source)
        audio = recognizer.listen(source)

        try:
            text = recognizer.recognize_google(audio)
            print("You said:", text)

            input_lang = detect_language(text)
            print("Detected language:", input_lang)

            text_in_english = translate_text(text, input_lang, "en")
            print("Translated to English:", text_in_english)

            if text_in_english == "Translation Error":
                speak("Sorry, I couldn't translate what you said.", lang=input_lang)
                return

            response_en = get_response(text_in_english)
            print("Assistant:", response_en)

            response_translated = translate_text(response_en, "en", input_lang)
            print("Translated response:", response_translated)

            speak(response_translated, lang=input_lang)

        except sr.UnknownValueError:
            print("Sorry, I could not understand your speech.")
        except sr.RequestError:
            print("Sorry, I am having trouble connecting to the speech service.")

# === Run the Assistant ===
if __name__ == "__main__":
    run_assistant()
