#!/usr/bin/env python3
"""
Installation script for Voice and Gesture Control System
Handles platform-specific dependency installation
"""

import sys
import subprocess
import platform
import os

def run_command(command, description=""):
    """Run a command and handle errors"""
    print(f"Running: {command}")
    if description:
        print(f"Description: {description}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print("✓ Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def install_basic_packages():
    """Install basic packages that work on all platforms"""
    print("\n" + "="*50)
    print("Installing basic packages...")
    print("="*50)
    
    basic_packages = [
        "numpy>=1.24.0",
        "pyyaml>=6.0.1",
        "flask>=2.3.0",
        "flask-socketio>=5.3.0",
        "psutil>=5.9.0",
        "pillow>=10.0.0",
        "speechrecognition>=3.10.0",
        "pyautogui>=0.9.54"
    ]
    
    for package in basic_packages:
        success = run_command(f"pip install {package}", f"Installing {package}")
        if not success:
            print(f"Warning: Failed to install {package}")

def install_opencv():
    """Install OpenCV"""
    print("\n" + "="*50)
    print("Installing OpenCV...")
    print("="*50)
    
    # Try opencv-python first
    success = run_command("pip install opencv-python>=4.8.0", "Installing OpenCV")
    if not success:
        print("Trying alternative OpenCV installation...")
        run_command("pip install opencv-python-headless>=4.8.0", "Installing OpenCV (headless)")

def install_mediapipe():
    """Install MediaPipe"""
    print("\n" + "="*50)
    print("Installing MediaPipe...")
    print("="*50)
    
    # Install the latest available version
    success = run_command("pip install mediapipe", "Installing MediaPipe (latest version)")
    if not success:
        print("Trying specific MediaPipe version...")
        run_command("pip install mediapipe==0.10.21", "Installing MediaPipe 0.10.21")

def install_pyaudio():
    """Install PyAudio with platform-specific handling"""
    print("\n" + "="*50)
    print("Installing PyAudio...")
    print("="*50)
    
    system = platform.system().lower()
    
    if system == "windows":
        print("Windows detected - trying pip install...")
        success = run_command("pip install pyaudio", "Installing PyAudio")
        
        if not success:
            print("\nPyAudio installation failed. Please try one of these solutions:")
            print("1. Install Microsoft Visual C++ Build Tools")
            print("2. Download PyAudio wheel from: https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio")
            print("3. Use conda: conda install pyaudio")
            
    elif system == "linux":
        print("Linux detected - installing system dependencies first...")
        
        # Try to install system dependencies
        distro_commands = [
            "sudo apt-get update && sudo apt-get install -y python3-pyaudio portaudio19-dev",
            "sudo yum install -y python3-pyaudio portaudio-devel",
            "sudo pacman -S python-pyaudio portaudio"
        ]
        
        for cmd in distro_commands:
            if run_command(cmd, "Installing system audio dependencies"):
                break
        
        # Install PyAudio via pip
        run_command("pip install pyaudio", "Installing PyAudio")
        
    elif system == "darwin":  # macOS
        print("macOS detected - installing with Homebrew...")
        
        # Install portaudio with Homebrew
        run_command("brew install portaudio", "Installing PortAudio with Homebrew")
        
        # Install PyAudio
        run_command("pip install pyaudio", "Installing PyAudio")
    
    else:
        print(f"Unknown system: {system}")
        print("Trying generic PyAudio installation...")
        run_command("pip install pyaudio", "Installing PyAudio")

def install_pyqt():
    """Install PyQt5"""
    print("\n" + "="*50)
    print("Installing PyQt5...")
    print("="*50)
    
    success = run_command("pip install PyQt5>=5.15.9", "Installing PyQt5")
    
    if not success:
        print("Trying alternative PyQt5 installation...")
        run_command("pip install PyQt5-Qt5>=5.15.0", "Installing PyQt5-Qt5")

def install_optional_packages():
    """Install optional packages"""
    print("\n" + "="*50)
    print("Installing optional packages...")
    print("="*50)
    
    optional_packages = [
        "librosa>=0.10.0",
        "sounddevice>=0.4.6",
        "scikit-learn>=1.3.0",
        "pystray>=0.19.4"
    ]
    
    for package in optional_packages:
        success = run_command(f"pip install {package}", f"Installing {package} (optional)")
        if not success:
            print(f"Warning: Optional package {package} failed to install")

def verify_installation():
    """Verify that key packages are installed"""
    print("\n" + "="*50)
    print("Verifying installation...")
    print("="*50)
    
    test_imports = [
        ("speech_recognition", "SpeechRecognition"),
        ("cv2", "OpenCV"),
        ("mediapipe", "MediaPipe"),
        ("pyautogui", "PyAutoGUI"),
        ("yaml", "PyYAML"),
        ("flask", "Flask"),
        ("numpy", "NumPy")
    ]
    
    failed_imports = []
    
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✓ {name} imported successfully")
        except ImportError as e:
            print(f"✗ {name} import failed: {e}")
            failed_imports.append(name)
    
    # Test PyAudio separately (it's often problematic)
    try:
        import pyaudio
        print("✓ PyAudio imported successfully")
    except ImportError as e:
        print(f"✗ PyAudio import failed: {e}")
        failed_imports.append("PyAudio")
    
    # Test PyQt5 separately
    try:
        from PyQt5.QtWidgets import QApplication
        print("✓ PyQt5 imported successfully")
    except ImportError as e:
        print(f"✗ PyQt5 import failed: {e}")
        failed_imports.append("PyQt5")
    
    return failed_imports

def main():
    """Main installation process"""
    print("Voice and Gesture Control System - Dependency Installer")
    print("="*60)
    print(f"Python version: {sys.version}")
    print(f"Platform: {platform.system()} {platform.release()}")
    print("="*60)
    
    # Install packages in order of dependency
    install_basic_packages()
    install_opencv()
    install_mediapipe()
    install_pyaudio()
    install_pyqt()
    install_optional_packages()
    
    # Verify installation
    failed_imports = verify_installation()
    
    print("\n" + "="*60)
    print("INSTALLATION SUMMARY")
    print("="*60)
    
    if not failed_imports:
        print("🎉 All dependencies installed successfully!")
        print("\nYou can now run the system with:")
        print("  python main.py")
        print("\nOr test the installation with:")
        print("  python test_system.py")
    else:
        print(f"⚠️  Installation completed with {len(failed_imports)} issues:")
        for package in failed_imports:
            print(f"  - {package}")
        
        print("\nPlease check the error messages above and:")
        print("1. Install missing system dependencies")
        print("2. Try manual installation of failed packages")
        print("3. Check the README.md for platform-specific instructions")
    
    print("\nFor help, refer to the README.md file or the troubleshooting section.")

if __name__ == "__main__":
    main()
